import os
# OpenSSL 3.0 호환성 문제 해결
os.environ['CRYPTOGRAPHY_OPENSSL_NO_LEGACY'] = '1'

from kivy.app import App
from kivy.lang import Builder
from kivy.core.text import LabelBase
from kivy.clock import Clock
from drive_sync import upload_to_drive, download_from_drive, show_save_folder, check_save_folder, local_backup, local_restore
from settings_ui import show_settings_popup

# 한글 폰트 등록
LabelBase.register(name='NanumGothic',
                   fn_regular='C:/Windows/Fonts/malgun.ttf')  # 맑은 고딕

class StardewSyncApp(App):
    def build(self):
        self.root = Builder.load_file('ui.kv')
        # 앱 시작 시 상태 확인
        Clock.schedule_once(self.update_status, 1)
        return self.root

    def sync_upload(self):
        upload_to_drive()

    def sync_download(self):
        download_from_drive()

    def open_save_folder(self):
        show_save_folder()

    def refresh_status(self):
        self.update_status(0)

    def local_backup(self):
        local_backup()

    def local_restore(self):
        local_restore()

    def show_settings(self):
        show_settings_popup()

    def update_status(self, dt):
        """상태 라벨 업데이트"""
        try:
            status_text = check_save_folder()
            if self.root:
                status_label = self.root.ids.get('status_label')
                if status_label:
                    status_label.text = status_text
        except Exception as e:
            print(f"상태 업데이트 오류: {e}")

if __name__ == '__main__':
    StardewSyncApp().run()