import os
# OpenSSL 3.0 호환성 문제 해결
os.environ['CRYPTOGRAPHY_OPENSSL_NO_LEGACY'] = '1'

from kivy.app import App
from kivy.lang import Builder
from drive_sync import upload_to_drive, download_from_drive

Builder.load_file('ui.kv')

class StardewSyncApp(App):
    def build(self):
        return self.root

    def sync_upload(self):
        upload_to_drive()

    def sync_download(self):
        download_from_drive()

if __name__ == '__main__':
    StardewSyncApp().run()