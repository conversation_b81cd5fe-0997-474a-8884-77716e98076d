import os
import json
from pathlib import Path

# 기본 설정값
DEFAULT_SETTINGS = {
    "stardew_save_path": os.path.expanduser("~\\AppData\\Roaming\\StardewValley\\Saves"),
    "backup_path": os.path.expanduser("~/Documents/StardewValley_Backup"),
    "output_path": os.path.expanduser("~/Desktop"),
    "save_filename": "StardewValley_Save.zip"
}

SETTINGS_FILE = "app_settings.json"

def load_settings():
    """설정 파일 로드"""
    try:
        if os.path.exists(SETTINGS_FILE):
            with open(SETTINGS_FILE, 'r', encoding='utf-8') as f:
                settings = json.load(f)
                # 기본값과 병합
                for key, value in DEFAULT_SETTINGS.items():
                    if key not in settings:
                        settings[key] = value
                return settings
        else:
            return DEFAULT_SETTINGS.copy()
    except Exception as e:
        print(f"설정 로드 오류: {e}")
        return DEFAULT_SETTINGS.copy()

def save_settings(settings):
    """설정 파일 저장"""
    try:
        with open(SETTINGS_FILE, 'w', encoding='utf-8') as f:
            json.dump(settings, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"설정 저장 오류: {e}")
        return False

def get_setting(key):
    """특정 설정값 가져오기"""
    settings = load_settings()
    return settings.get(key, DEFAULT_SETTINGS.get(key))

def set_setting(key, value):
    """특정 설정값 저장"""
    settings = load_settings()
    settings[key] = value
    return save_settings(settings)

def validate_paths():
    """경로 유효성 검사"""
    settings = load_settings()
    issues = []
    
    # Stardew Valley 세이브 경로 확인
    stardew_path = settings.get("stardew_save_path")
    if not os.path.exists(stardew_path):
        issues.append(f"Stardew Valley 세이브 폴더를 찾을 수 없습니다: {stardew_path}")
    
    # 백업 경로 확인 (없으면 생성 시도)
    backup_path = settings.get("backup_path")
    try:
        os.makedirs(backup_path, exist_ok=True)
    except Exception as e:
        issues.append(f"백업 폴더 생성 실패: {backup_path} - {e}")
    
    # 출력 경로 확인
    output_path = settings.get("output_path")
    if not os.path.exists(output_path):
        issues.append(f"출력 폴더를 찾을 수 없습니다: {output_path}")
    
    return issues

def auto_detect_stardew_path():
    """Stardew Valley 세이브 경로 자동 감지"""
    possible_paths = [
        os.path.expanduser("~\\AppData\\Roaming\\StardewValley\\Saves"),
        os.path.expanduser("~\\Documents\\My Games\\Stardew Valley\\Saves"),
        os.path.expanduser("~\\AppData\\Local\\StardewValley\\Saves"),
        "C:\\Program Files (x86)\\Steam\\userdata\\*\\413150\\remote",
        "C:\\Program Files\\Steam\\userdata\\*\\413150\\remote"
    ]
    
    for path in possible_paths:
        if "*" in path:
            # Steam 경로의 경우 와일드카드 처리
            import glob
            matches = glob.glob(path)
            for match in matches:
                if os.path.exists(match):
                    return match
        else:
            if os.path.exists(path):
                return path
    
    return None
