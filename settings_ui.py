from kivy.uix.popup import Popup
from kivy.uix.label import Label
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.button import Button
from kivy.uix.textinput import TextInput
from kivy.uix.filechooser import FileChooserListView
from kivy.uix.gridlayout import GridLayout
from settings_manager import load_settings, save_settings, auto_detect_stardew_path, validate_paths
import os

def show_settings_popup():
    """설정 팝업 표시"""
    settings = load_settings()
    
    content = BoxLayout(orientation='vertical', spacing=10, padding=10)
    
    # 제목
    title_label = Label(
        text="⚙️ 설정",
        font_name='NanumGothic',
        font_size=20,
        size_hint_y=0.1,
        color=[0.2, 0.6, 1, 1]
    )
    content.add_widget(title_label)
    
    # 설정 항목들
    settings_layout = GridLayout(cols=1, spacing=10, size_hint_y=0.7)
    
    # Stardew Valley 세이브 경로
    stardew_layout = BoxLayout(orientation='horizontal', spacing=5, size_hint_y=None, height=40)
    stardew_label = Label(text="세이브 경로:", font_name='NanumGothic', size_hint_x=0.3)
    stardew_input = TextInput(
        text=settings.get("stardew_save_path", ""),
        font_name='NanumGothic',
        size_hint_x=0.6,
        multiline=False
    )
    stardew_browse = Button(
        text="📁",
        size_hint_x=0.1,
        font_name='NanumGothic'
    )
    stardew_layout.add_widget(stardew_label)
    stardew_layout.add_widget(stardew_input)
    stardew_layout.add_widget(stardew_browse)
    settings_layout.add_widget(stardew_layout)
    
    # 백업 경로
    backup_layout = BoxLayout(orientation='horizontal', spacing=5, size_hint_y=None, height=40)
    backup_label = Label(text="백업 경로:", font_name='NanumGothic', size_hint_x=0.3)
    backup_input = TextInput(
        text=settings.get("backup_path", ""),
        font_name='NanumGothic',
        size_hint_x=0.6,
        multiline=False
    )
    backup_browse = Button(
        text="📁",
        size_hint_x=0.1,
        font_name='NanumGothic'
    )
    backup_layout.add_widget(backup_label)
    backup_layout.add_widget(backup_input)
    backup_layout.add_widget(backup_browse)
    settings_layout.add_widget(backup_layout)
    
    # 출력 경로
    output_layout = BoxLayout(orientation='horizontal', spacing=5, size_hint_y=None, height=40)
    output_label = Label(text="출력 경로:", font_name='NanumGothic', size_hint_x=0.3)
    output_input = TextInput(
        text=settings.get("output_path", ""),
        font_name='NanumGothic',
        size_hint_x=0.6,
        multiline=False
    )
    output_browse = Button(
        text="📁",
        size_hint_x=0.1,
        font_name='NanumGothic'
    )
    output_layout.add_widget(output_label)
    output_layout.add_widget(output_input)
    output_layout.add_widget(output_browse)
    settings_layout.add_widget(output_layout)
    
    # 파일명
    filename_layout = BoxLayout(orientation='horizontal', spacing=5, size_hint_y=None, height=40)
    filename_label = Label(text="파일명:", font_name='NanumGothic', size_hint_x=0.3)
    filename_input = TextInput(
        text=settings.get("save_filename", ""),
        font_name='NanumGothic',
        size_hint_x=0.7,
        multiline=False
    )
    filename_layout.add_widget(filename_label)
    filename_layout.add_widget(filename_input)
    settings_layout.add_widget(filename_layout)
    
    content.add_widget(settings_layout)
    
    # 버튼들
    button_layout = BoxLayout(orientation='horizontal', spacing=10, size_hint_y=0.2)
    
    auto_detect_btn = Button(
        text="🔍 자동 감지",
        font_name='NanumGothic',
        background_color=[0.2, 0.7, 0.2, 1]
    )
    
    save_btn = Button(
        text="💾 저장",
        font_name='NanumGothic',
        background_color=[0.2, 0.5, 0.8, 1]
    )
    
    cancel_btn = Button(
        text="❌ 취소",
        font_name='NanumGothic',
        background_color=[0.7, 0.7, 0.7, 1]
    )
    
    button_layout.add_widget(auto_detect_btn)
    button_layout.add_widget(save_btn)
    button_layout.add_widget(cancel_btn)
    content.add_widget(button_layout)
    
    popup = Popup(
        title="설정",
        content=content,
        size_hint=(0.9, 0.8)
    )
    
    def browse_folder(input_widget, title):
        """폴더 선택 다이얼로그"""
        def show_folder_chooser():
            folder_content = BoxLayout(orientation='vertical', spacing=10, padding=10)
            
            filechooser = FileChooserListView(
                path=os.path.expanduser("~"),
                dirselect=True
            )
            folder_content.add_widget(filechooser)
            
            folder_button_layout = BoxLayout(orientation='horizontal', spacing=10, size_hint_y=0.2)
            
            select_btn = Button(text="선택", font_name='NanumGothic')
            cancel_folder_btn = Button(text="취소", font_name='NanumGothic')
            
            folder_button_layout.add_widget(select_btn)
            folder_button_layout.add_widget(cancel_folder_btn)
            folder_content.add_widget(folder_button_layout)
            
            folder_popup = Popup(
                title=title,
                content=folder_content,
                size_hint=(0.9, 0.8)
            )
            
            def select_folder(instance):
                if filechooser.selection:
                    selected_path = filechooser.selection[0]
                    input_widget.text = selected_path
                folder_popup.dismiss()
            
            def cancel_folder(instance):
                folder_popup.dismiss()
            
            select_btn.bind(on_press=select_folder)
            cancel_folder_btn.bind(on_press=cancel_folder)
            
            folder_popup.open()
        
        show_folder_chooser()
    
    def auto_detect(instance):
        """자동 감지"""
        detected_path = auto_detect_stardew_path()
        if detected_path:
            stardew_input.text = detected_path
            show_popup_message("자동 감지 성공", f"Stardew Valley 세이브 경로를 찾았습니다:\n{detected_path}")
        else:
            show_popup_message("자동 감지 실패", "Stardew Valley 세이브 경로를 찾을 수 없습니다.\n수동으로 설정해주세요.")
    
    def save_settings_action(instance):
        """설정 저장"""
        new_settings = {
            "stardew_save_path": stardew_input.text,
            "backup_path": backup_input.text,
            "output_path": output_input.text,
            "save_filename": filename_input.text
        }
        
        if save_settings(new_settings):
            popup.dismiss()
            show_popup_message("설정 저장", "설정이 성공적으로 저장되었습니다!")
        else:
            show_popup_message("설정 오류", "설정 저장에 실패했습니다.")
    
    def cancel(instance):
        popup.dismiss()
    
    # 이벤트 바인딩
    stardew_browse.bind(on_press=lambda x: browse_folder(stardew_input, "Stardew Valley 세이브 폴더 선택"))
    backup_browse.bind(on_press=lambda x: browse_folder(backup_input, "백업 폴더 선택"))
    output_browse.bind(on_press=lambda x: browse_folder(output_input, "출력 폴더 선택"))
    auto_detect_btn.bind(on_press=auto_detect)
    save_btn.bind(on_press=save_settings_action)
    cancel_btn.bind(on_press=cancel)
    
    popup.open()

def show_popup_message(title, message):
    """간단한 메시지 팝업"""
    popup = Popup(
        title=title,
        content=Label(text=message, font_name='NanumGothic', text_size=(400, None), halign='center'),
        size_hint=(0.6, 0.4)
    )
    popup.open()
