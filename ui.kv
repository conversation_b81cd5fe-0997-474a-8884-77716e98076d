BoxLayout:
    orientation: 'vertical'
    padding: 20
    spacing: 15

    # 제목
    Label:
        text: "🎮 Stardew Valley Save Sync"
        font_name: 'NanumGothic'
        font_size: 28
        size_hint_y: 0.2
        color: [0.2, 0.7, 0.2, 1]
        bold: True

    # 상태 표시
    Label:
        id: status_label
        text: "세이브 폴더 상태를 확인하세요"
        font_name: 'NanumGothic'
        font_size: 14
        size_hint_y: 0.15
        color: [0.6, 0.6, 0.6, 1]
        text_size: self.size
        halign: 'center'

    # 메인 버튼들
    GridLayout:
        cols: 1
        spacing: 10
        size_hint_y: 0.5

        Button:
            text: "📤 업로드 (PC → Google Drive)"
            font_name: 'NanumGothic'
            font_size: 16
            background_color: [0.2, 0.7, 0.2, 1]
            on_press: app.sync_upload()

        Button:
            text: "📥 다운로드 (Google Drive → PC)"
            font_name: 'NanumGothic'
            font_size: 16
            background_color: [0.2, 0.5, 0.8, 1]
            on_press: app.sync_download()

    # 유틸리티 버튼들
    GridLayout:
        cols: 2
        spacing: 10
        size_hint_y: 0.15

        Button:
            text: "📁 세이브 폴더 열기"
            font_name: 'NanumGothic'
            font_size: 12
            background_color: [0.7, 0.7, 0.7, 1]
            on_press: app.open_save_folder()

        Button:
            text: "🔄 상태 새로고침"
            font_name: 'NanumGothic'
            font_size: 12
            background_color: [0.7, 0.7, 0.7, 1]
            on_press: app.refresh_status()