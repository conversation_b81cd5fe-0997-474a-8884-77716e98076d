BoxLayout:
    orientation: 'vertical'
    padding: 20
    spacing: 15

    # 제목
    Label:
        text: "🎮 Stardew Valley Save Sync"
        font_name: 'NanumGothic'
        font_size: 28
        size_hint_y: 0.2
        color: [0.2, 0.7, 0.2, 1]
        bold: True

    # 상태 표시
    Label:
        id: status_label
        text: "세이브 폴더 상태를 확인하세요"
        font_name: 'NanumGothic'
        font_size: 14
        size_hint_y: 0.15
        color: [0.6, 0.6, 0.6, 1]
        text_size: self.size
        halign: 'center'

    # 메인 버튼들
    GridLayout:
        cols: 1
        spacing: 8
        size_hint_y: 0.6

        # Google Drive 섹션
        Label:
            text: "🌐 Google Drive 동기화"
            font_name: 'NanumGothic'
            font_size: 14
            size_hint_y: 0.15
            color: [0.3, 0.3, 0.3, 1]

        GridLayout:
            cols: 2
            spacing: 5
            size_hint_y: 0.25

            Button:
                text: "📤 클라우드 업로드"
                font_name: 'NanumGothic'
                font_size: 14
                background_color: [0.2, 0.7, 0.2, 1]
                on_press: app.sync_upload()

            Button:
                text: "📥 클라우드 다운로드"
                font_name: 'NanumGothic'
                font_size: 14
                background_color: [0.2, 0.5, 0.8, 1]
                on_press: app.sync_download()

        # 로컬 백업 섹션
        Label:
            text: "💾 로컬 백업 (간편 모드)"
            font_name: 'NanumGothic'
            font_size: 14
            size_hint_y: 0.15
            color: [0.3, 0.3, 0.3, 1]

        GridLayout:
            cols: 2
            spacing: 5
            size_hint_y: 0.25

            Button:
                text: "💾 로컬 백업"
                font_name: 'NanumGothic'
                font_size: 14
                background_color: [0.8, 0.6, 0.2, 1]
                on_press: app.local_backup()

            Button:
                text: "🔄 백업 복원"
                font_name: 'NanumGothic'
                font_size: 14
                background_color: [0.6, 0.4, 0.8, 1]
                on_press: app.local_restore()

    # 유틸리티 버튼들
    GridLayout:
        cols: 3
        spacing: 10
        size_hint_y: 0.15

        Button:
            text: "📁 세이브 폴더"
            font_name: 'NanumGothic'
            font_size: 11
            background_color: [0.7, 0.7, 0.7, 1]
            on_press: app.open_save_folder()

        Button:
            text: "🔄 새로고침"
            font_name: 'NanumGothic'
            font_size: 11
            background_color: [0.7, 0.7, 0.7, 1]
            on_press: app.refresh_status()

        Button:
            text: "⚙️ 설정"
            font_name: 'NanumGothic'
            font_size: 11
            background_color: [0.8, 0.4, 0.2, 1]
            on_press: app.show_settings()