from pydrive2.auth import GoogleAuth
from pydrive2.drive import GoogleDrive
import os

SAVEFILE_PATH = "SaveFile.zip"  # Windows 테스트용 경로

def upload_to_drive():
    gauth = GoogleAuth()
    gauth.LocalWebserverAuth()
    drive = GoogleDrive(gauth)

    file = drive.CreateFile({'title': 'SaveFile.zip'})
    file.SetContentFile(SAVEFILE_PATH)
    file.Upload()
    print("✅ 업로드 완료")

def download_from_drive():
    gauth = GoogleAuth()
    gauth.LocalWebserverAuth()
    drive = GoogleDrive(gauth)

    file_list = drive.ListFile({'q': "'root' in parents and trashed=false"}).GetList()
    for f in file_list:
        if f['title'] == 'SaveFile.zip':
            f.GetContentFile(SAVEFILE_PATH)
            print("✅ 다운로드 완료")
            break