import os
import zipfile
import shutil
from pathlib import Path
from kivy.uix.popup import Popup
from kivy.uix.label import Label
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.button import Button
from kivy.uix.filechooser import FileChooserListView
from kivy.clock import Clock
import threading
from settings_manager import get_setting, set_setting, validate_paths, auto_detect_stardew_path

TEMP_SAVE_PATH = "temp_save"

def show_popup(title, message, auto_dismiss=True):
    """팝업 메시지 표시"""
    popup = Popup(title=title,
                  content=Label(text=message, font_name='NanumGothic', text_size=(400, None), halign='center'),
                  size_hint=(0.7, 0.5),
                  auto_dismiss=auto_dismiss)
    popup.open()
    return popup

def create_save_archive():
    """Stardew Valley 세이브 파일들을 압축"""
    try:
        stardew_save_path = get_setting("stardew_save_path")
        save_filename = get_setting("save_filename")

        if not os.path.exists(stardew_save_path):
            return None, f"Stardew Valley 세이브 폴더를 찾을 수 없습니다: {stardew_save_path}"

        # 임시 폴더 생성
        if os.path.exists(TEMP_SAVE_PATH):
            shutil.rmtree(TEMP_SAVE_PATH)
        os.makedirs(TEMP_SAVE_PATH)

        # 세이브 파일들을 압축
        with zipfile.ZipFile(save_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(stardew_save_path):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, stardew_save_path)
                    zipf.write(file_path, arcname)

        return save_filename, None
    except Exception as e:
        return None, f"압축 생성 실패: {str(e)}"

def extract_save_archive(zip_path):
    """압축 파일을 Stardew Valley 세이브 폴더에 추출"""
    try:
        stardew_save_path = get_setting("stardew_save_path")

        # 백업 생성
        backup_path = f"{stardew_save_path}_backup"
        if os.path.exists(stardew_save_path):
            if os.path.exists(backup_path):
                shutil.rmtree(backup_path)
            shutil.copytree(stardew_save_path, backup_path)

        # 기존 세이브 폴더 삭제
        if os.path.exists(stardew_save_path):
            shutil.rmtree(stardew_save_path)
        os.makedirs(stardew_save_path)

        # 압축 해제
        with zipfile.ZipFile(zip_path, 'r') as zipf:
            zipf.extractall(stardew_save_path)

        return True, None
    except Exception as e:
        return False, f"압축 해제 실패: {str(e)}"

def check_google_api_setup():
    """Google API 설정 파일 확인"""
    if not os.path.exists("client_secrets.json"):
        return False, "client_secrets.json 파일이 없습니다."

    try:
        import json
        with open("client_secrets.json", 'r') as f:
            config = json.load(f)
            client_id = config.get("installed", {}).get("client_id", "")
            if "YOUR_CLIENT_ID" in client_id or not client_id:
                return False, "client_secrets.json에 실제 클라이언트 ID를 설정해주세요."
        return True, None
    except Exception as e:
        return False, f"설정 파일 오류: {str(e)}"

def init_google_drive():
    """Google Drive 인증 및 초기화"""
    try:
        # API 설정 확인
        is_setup, error = check_google_api_setup()
        if not is_setup:
            return None, error

        from pydrive2.auth import GoogleAuth
        from pydrive2.drive import GoogleDrive

        gauth = GoogleAuth()

        # 저장된 인증 정보가 있으면 로드
        if os.path.exists("credentials.json"):
            gauth.LoadCredentialsFile("credentials.json")

        # 인증이 필요하면 웹 브라우저를 통해 인증
        if gauth.credentials is None:
            gauth.LocalWebserverAuth()
        elif gauth.access_token_expired:
            gauth.Refresh()
        else:
            gauth.Authorize()

        # 인증 정보 저장
        gauth.SaveCredentialsFile("credentials.json")

        drive = GoogleDrive(gauth)
        return drive, None
    except Exception as e:
        return None, f"Google Drive 인증 실패: {str(e)}"

def upload_to_drive():
    """업로드 기능 - 파일을 생성하여 사용자가 직접 Google Drive에 업로드"""
    def upload_thread():
        try:
            # 진행 상황 팝업 표시
            progress_popup = show_popup("파일 생성 중", "세이브 파일을 압축하고 있습니다...", auto_dismiss=False)

            # 세이브 파일 압축
            zip_path, error = create_save_archive()
            if error:
                progress_popup.dismiss()
                show_popup("오류", error)
                return

            # 출력 경로에 파일 복사
            output_path = get_setting("output_path")
            save_filename = get_setting("save_filename")
            output_file_path = os.path.join(output_path, save_filename)
            shutil.copy2(zip_path, output_file_path)

            # 임시 파일 정리
            if os.path.exists(zip_path):
                os.remove(zip_path)

            progress_popup.dismiss()

            # 성공 메시지와 함께 Google Drive 웹사이트 열기
            Clock.schedule_once(lambda dt: show_upload_success_popup(output_file_path))

        except Exception as e:
            error_msg = f"파일 생성 실패: {str(e)}"
            if 'progress_popup' in locals():
                progress_popup.dismiss()
            Clock.schedule_once(lambda dt: show_popup("오류", error_msg))

    # 백그라운드에서 실행
    thread = threading.Thread(target=upload_thread)
    thread.daemon = True
    thread.start()

def show_upload_success_popup(file_path):
    """업로드 성공 팝업 - Google Drive 링크 포함"""

    content = BoxLayout(orientation='vertical', spacing=10, padding=10)

    # 메시지 라벨
    message = Label(
        text=f"세이브 파일이 데스크톱에 생성되었습니다!\n\n파일: {os.path.basename(file_path)}\n\n아래 버튼을 클릭하여 Google Drive에 직접 업로드하세요.",
        font_name='NanumGothic',
        text_size=(400, None),
        halign='center'
    )
    content.add_widget(message)

    # 버튼 레이아웃
    button_layout = BoxLayout(orientation='horizontal', spacing=10, size_hint_y=0.3)

    # Google Drive 열기 버튼
    drive_button = Button(
        text="🌐 Google Drive 열기",
        font_name='NanumGothic',
        background_color=[0.2, 0.7, 0.2, 1]
    )

    # 파일 폴더 열기 버튼
    folder_button = Button(
        text="📁 파일 위치 열기",
        font_name='NanumGothic',
        background_color=[0.7, 0.7, 0.7, 1]
    )

    button_layout.add_widget(drive_button)
    button_layout.add_widget(folder_button)
    content.add_widget(button_layout)

    popup = Popup(
        title="업로드 준비 완료",
        content=content,
        size_hint=(0.8, 0.6)
    )

    def open_google_drive(instance):
        import webbrowser
        webbrowser.open("https://drive.google.com/drive/my-drive")
        popup.dismiss()

    def open_file_location(instance):
        folder_path = os.path.dirname(file_path)
        os.startfile(folder_path)
        popup.dismiss()

    drive_button.bind(on_press=open_google_drive)
    folder_button.bind(on_press=open_file_location)

    popup.open()

def download_from_drive():
    """다운로드 기능 - 사용자가 Google Drive에서 파일을 직접 다운로드"""
    show_download_instruction_popup()

def show_download_instruction_popup():
    """다운로드 안내 팝업"""

    content = BoxLayout(orientation='vertical', spacing=10, padding=10)

    # 안내 메시지
    message = Label(
        text="Google Drive에서 세이브 파일을 다운로드하세요:\n\n1. 아래 버튼으로 Google Drive를 엽니다\n2. 'StardewValley_Save.zip' 파일을 찾습니다\n3. 파일을 다운로드합니다\n4. '파일 선택' 버튼으로 다운로드한 파일을 선택합니다",
        font_name='NanumGothic',
        text_size=(400, None),
        halign='center'
    )
    content.add_widget(message)

    # 버튼 레이아웃
    button_layout = BoxLayout(orientation='horizontal', spacing=10, size_hint_y=0.3)

    # Google Drive 열기 버튼
    drive_button = Button(
        text="🌐 Google Drive 열기",
        font_name='NanumGothic',
        background_color=[0.2, 0.7, 0.2, 1]
    )

    # 파일 선택 버튼
    select_button = Button(
        text="📁 파일 선택하기",
        font_name='NanumGothic',
        background_color=[0.2, 0.5, 0.8, 1]
    )

    button_layout.add_widget(drive_button)
    button_layout.add_widget(select_button)
    content.add_widget(button_layout)

    popup = Popup(
        title="Google Drive에서 다운로드",
        content=content,
        size_hint=(0.8, 0.6)
    )

    def open_google_drive(instance):
        import webbrowser
        webbrowser.open("https://drive.google.com/drive/my-drive")

    def select_file(instance):
        popup.dismiss()
        show_file_chooser()

    drive_button.bind(on_press=open_google_drive)
    select_button.bind(on_press=select_file)

    popup.open()

def show_file_chooser():
    """파일 선택 다이얼로그"""
    content = BoxLayout(orientation='vertical', spacing=10, padding=10)

    # 파일 선택기
    filechooser = FileChooserListView(
        path=os.path.expanduser("~/Downloads"),
        filters=['*.zip']
    )
    content.add_widget(filechooser)

    # 버튼 레이아웃
    button_layout = BoxLayout(orientation='horizontal', spacing=10, size_hint_y=0.2)

    restore_button = Button(
        text="복원하기",
        font_name='NanumGothic',
        background_color=[0.2, 0.7, 0.2, 1]
    )

    cancel_button = Button(
        text="취소",
        font_name='NanumGothic',
        background_color=[0.7, 0.7, 0.7, 1]
    )

    button_layout.add_widget(restore_button)
    button_layout.add_widget(cancel_button)
    content.add_widget(button_layout)

    popup = Popup(
        title="세이브 파일 선택",
        content=content,
        size_hint=(0.9, 0.8)
    )

    def restore_save(instance):
        if filechooser.selection:
            selected_file = filechooser.selection[0]
            popup.dismiss()
            restore_from_file(selected_file)
        else:
            show_popup("오류", "파일을 선택해주세요.")

    def cancel(instance):
        popup.dismiss()

    restore_button.bind(on_press=restore_save)
    cancel_button.bind(on_press=cancel)

    popup.open()

def restore_from_file(file_path):
    """선택한 파일에서 세이브 복원"""
    def restore_thread():
        try:
            progress_popup = show_popup("복원 중", "세이브 파일을 복원하고 있습니다...", auto_dismiss=False)

            success, error = extract_save_archive(file_path)
            progress_popup.dismiss()

            if success:
                Clock.schedule_once(lambda dt: show_popup("복원 성공", "세이브 파일이 성공적으로 복원되었습니다!"))
            else:
                Clock.schedule_once(lambda dt: show_popup("복원 오류", error))

        except Exception as e:
            error_msg = f"복원 실패: {str(e)}"
            if 'progress_popup' in locals():
                progress_popup.dismiss()
            Clock.schedule_once(lambda dt: show_popup("복원 오류", error_msg))

    thread = threading.Thread(target=restore_thread)
    thread.daemon = True
    thread.start()

def show_save_folder():
    """세이브 폴더 열기"""
    try:
        stardew_save_path = get_setting("stardew_save_path")
        if os.path.exists(stardew_save_path):
            os.startfile(stardew_save_path)
        else:
            show_popup("오류", f"Stardew Valley 세이브 폴더를 찾을 수 없습니다: {stardew_save_path}")
    except Exception as e:
        show_popup("오류", f"폴더 열기 실패: {str(e)}")

def check_save_folder():
    """세이브 폴더 상태 확인"""
    stardew_save_path = get_setting("stardew_save_path")
    if os.path.exists(stardew_save_path):
        save_folders = [f for f in os.listdir(stardew_save_path) if os.path.isdir(os.path.join(stardew_save_path, f))]
        if save_folders:
            return f"세이브 폴더 발견: {len(save_folders)}개의 세이브 파일"
        else:
            return "세이브 폴더가 비어있습니다"
    else:
        return f"Stardew Valley 세이브 폴더를 찾을 수 없습니다: {stardew_save_path}"

# 로컬 동기화 기능 (Google API 대안)

def create_local_backup():
    """로컬 백업 폴더에 세이브 파일 백업"""
    try:
        local_sync_path = get_setting("backup_path")

        # 백업 폴더 생성
        if not os.path.exists(local_sync_path):
            os.makedirs(local_sync_path)

        # 현재 시간으로 백업 파일명 생성
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"StardewValley_Save_{timestamp}.zip"
        backup_path = os.path.join(local_sync_path, backup_filename)

        # 세이브 파일 압축
        zip_path, error = create_save_archive()
        if error:
            return None, error

        # 백업 폴더로 복사
        shutil.copy2(zip_path, backup_path)

        # 임시 파일 삭제
        if os.path.exists(zip_path):
            os.remove(zip_path)

        return backup_path, None
    except Exception as e:
        return None, f"로컬 백업 실패: {str(e)}"

def restore_from_local_backup():
    """로컬 백업에서 가장 최근 세이브 복원"""
    try:
        local_sync_path = get_setting("backup_path")

        if not os.path.exists(local_sync_path):
            return False, "백업 폴더가 존재하지 않습니다."

        # 백업 파일 목록 가져오기
        backup_files = [f for f in os.listdir(local_sync_path) if f.endswith('.zip')]
        if not backup_files:
            return False, "백업 파일이 없습니다."

        # 가장 최근 백업 파일 선택
        backup_files.sort(reverse=True)
        latest_backup = os.path.join(local_sync_path, backup_files[0])

        # 세이브 복원
        success, error = extract_save_archive(latest_backup)
        if success:
            return True, f"백업 파일 {backup_files[0]}에서 복원 완료"
        else:
            return False, error
    except Exception as e:
        return False, f"로컬 복원 실패: {str(e)}"

def list_local_backups():
    """로컬 백업 파일 목록 반환"""
    try:
        local_sync_path = get_setting("backup_path")

        if not os.path.exists(local_sync_path):
            return []

        backup_files = [f for f in os.listdir(local_sync_path) if f.endswith('.zip')]
        backup_files.sort(reverse=True)
        return backup_files
    except Exception as e:
        return []

def open_backup_folder():
    """백업 폴더 열기"""
    try:
        local_sync_path = get_setting("backup_path")

        if not os.path.exists(local_sync_path):
            os.makedirs(local_sync_path)
        os.startfile(local_sync_path)
    except Exception as e:
        show_popup("오류", f"백업 폴더 열기 실패: {str(e)}")

# 간편한 로컬 동기화 함수들
def local_backup():
    """로컬 백업 (Google Drive 대안)"""
    def backup_thread():
        try:
            progress_popup = show_popup("백업 중", "세이브 파일을 백업하고 있습니다...", auto_dismiss=False)

            backup_path, error = create_local_backup()
            progress_popup.dismiss()

            if error:
                Clock.schedule_once(lambda dt: show_popup("백업 오류", error))
            else:
                filename = os.path.basename(backup_path)
                Clock.schedule_once(lambda dt: show_popup("백업 성공", f"백업 완료: {filename}"))
        except Exception as e:
            error_msg = f"백업 실패: {str(e)}"
            if 'progress_popup' in locals():
                progress_popup.dismiss()
            Clock.schedule_once(lambda dt: show_popup("백업 오류", error_msg))

    thread = threading.Thread(target=backup_thread)
    thread.daemon = True
    thread.start()

def local_restore():
    """로컬 복원 (Google Drive 대안)"""
    def restore_thread():
        try:
            progress_popup = show_popup("복원 중", "백업에서 세이브 파일을 복원하고 있습니다...", auto_dismiss=False)

            success, message = restore_from_local_backup()
            progress_popup.dismiss()

            if success:
                Clock.schedule_once(lambda dt: show_popup("복원 성공", message))
            else:
                Clock.schedule_once(lambda dt: show_popup("복원 오류", message))
        except Exception as e:
            error_msg = f"복원 실패: {str(e)}"
            if 'progress_popup' in locals():
                progress_popup.dismiss()
            Clock.schedule_once(lambda dt: show_popup("복원 오류", error_msg))

    thread = threading.Thread(target=restore_thread)
    thread.daemon = True
    thread.start()