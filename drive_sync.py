import os
from kivy.uix.popup import Popup
from kivy.uix.label import Label

SAVEFILE_PATH = "SaveFile.zip"  # Windows 테스트용 경로

def show_popup(title, message):
    """팝업 메시지 표시"""
    popup = Popup(title=title,
                  content=Label(text=message, font_name='NanumGothic'),
                  size_hint=(0.6, 0.4))
    popup.open()

def upload_to_drive():
    """업로드 기능 (테스트용)"""
    try:
        # 실제 Google Drive 업로드 로직이 들어갈 자리
        # 현재는 테스트용으로 메시지만 표시
        if os.path.exists(SAVEFILE_PATH):
            show_popup("Upload Success", f"File {SAVEFILE_PATH} uploaded successfully!")
        else:
            show_popup("Upload Error", f"File {SAVEFILE_PATH} not found!")
    except Exception as e:
        show_popup("Upload Error", f"Upload failed: {str(e)}")

def download_from_drive():
    """다운로드 기능 (테스트용)"""
    try:
        # 실제 Google Drive 다운로드 로직이 들어갈 자리
        # 현재는 테스트용으로 메시지만 표시
        show_popup("Download Success", f"File downloaded to {SAVEFILE_PATH}")
    except Exception as e:
        show_popup("Download Error", f"Download failed: {str(e)}")