import os
import zipfile
import shutil
from pathlib import Path
from kivy.uix.popup import Popup
from kivy.uix.label import Label
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.button import Button
from kivy.uix.filechooser import FileChooserListView
from kivy.clock import Clock
import threading

# Stardew Valley 기본 세이브 경로
STARDEW_SAVE_PATH = os.path.expanduser("~\\AppData\\Roaming\\StardewValley\\Saves")
TEMP_SAVE_PATH = "temp_save"
SAVEFILE_NAME = "StardewValley_Save.zip"

def show_popup(title, message, auto_dismiss=True):
    """팝업 메시지 표시"""
    popup = Popup(title=title,
                  content=Label(text=message, font_name='NanumGothic', text_size=(400, None), halign='center'),
                  size_hint=(0.7, 0.5),
                  auto_dismiss=auto_dismiss)
    popup.open()
    return popup

def create_save_archive():
    """Stardew Valley 세이브 파일들을 압축"""
    try:
        if not os.path.exists(STARDEW_SAVE_PATH):
            return None, "Stardew Valley 세이브 폴더를 찾을 수 없습니다."

        # 임시 폴더 생성
        if os.path.exists(TEMP_SAVE_PATH):
            shutil.rmtree(TEMP_SAVE_PATH)
        os.makedirs(TEMP_SAVE_PATH)

        # 세이브 파일들을 압축
        with zipfile.ZipFile(SAVEFILE_NAME, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(STARDEW_SAVE_PATH):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, STARDEW_SAVE_PATH)
                    zipf.write(file_path, arcname)

        return SAVEFILE_NAME, None
    except Exception as e:
        return None, f"압축 생성 실패: {str(e)}"

def extract_save_archive(zip_path):
    """압축 파일을 Stardew Valley 세이브 폴더에 추출"""
    try:
        # 백업 생성
        backup_path = f"{STARDEW_SAVE_PATH}_backup"
        if os.path.exists(STARDEW_SAVE_PATH):
            if os.path.exists(backup_path):
                shutil.rmtree(backup_path)
            shutil.copytree(STARDEW_SAVE_PATH, backup_path)

        # 기존 세이브 폴더 삭제
        if os.path.exists(STARDEW_SAVE_PATH):
            shutil.rmtree(STARDEW_SAVE_PATH)
        os.makedirs(STARDEW_SAVE_PATH)

        # 압축 해제
        with zipfile.ZipFile(zip_path, 'r') as zipf:
            zipf.extractall(STARDEW_SAVE_PATH)

        return True, None
    except Exception as e:
        return False, f"압축 해제 실패: {str(e)}"

def init_google_drive():
    """Google Drive 인증 및 초기화"""
    try:
        from pydrive2.auth import GoogleAuth
        from pydrive2.drive import GoogleDrive

        gauth = GoogleAuth()

        # 저장된 인증 정보가 있으면 로드
        if os.path.exists("credentials.json"):
            gauth.LoadCredentialsFile("credentials.json")

        # 인증이 필요하면 웹 브라우저를 통해 인증
        if gauth.credentials is None:
            gauth.LocalWebserverAuth()
        elif gauth.access_token_expired:
            gauth.Refresh()
        else:
            gauth.Authorize()

        # 인증 정보 저장
        gauth.SaveCredentialsFile("credentials.json")

        drive = GoogleDrive(gauth)
        return drive, None
    except Exception as e:
        return None, f"Google Drive 인증 실패: {str(e)}"

def upload_to_drive():
    """업로드 기능"""
    def upload_thread():
        try:
            # 진행 상황 팝업 표시
            progress_popup = show_popup("업로드 중", "세이브 파일을 압축하고 있습니다...", auto_dismiss=False)

            # 세이브 파일 압축
            zip_path, error = create_save_archive()
            if error:
                progress_popup.dismiss()
                show_popup("업로드 오류", error)
                return

            # Google Drive 초기화
            Clock.schedule_once(lambda dt: setattr(progress_popup.content, 'text', "Google Drive에 연결 중..."))
            drive, error = init_google_drive()
            if error:
                progress_popup.dismiss()
                show_popup("업로드 오류", error)
                return

            # 기존 파일 검색 및 삭제
            Clock.schedule_once(lambda dt: setattr(progress_popup.content, 'text', "기존 파일 확인 중..."))
            file_list = drive.ListFile({'q': f"title='{SAVEFILE_NAME}' and trashed=false"}).GetList()
            for file in file_list:
                file.Delete()

            # 새 파일 업로드
            Clock.schedule_once(lambda dt: setattr(progress_popup.content, 'text', "파일 업로드 중..."))
            file = drive.CreateFile({'title': SAVEFILE_NAME})
            file.SetContentFile(zip_path)
            file.Upload()

            # 임시 파일 정리
            if os.path.exists(zip_path):
                os.remove(zip_path)

            progress_popup.dismiss()
            Clock.schedule_once(lambda dt: show_popup("업로드 성공", "세이브 파일이 성공적으로 업로드되었습니다!"))

        except Exception as e:
            if 'progress_popup' in locals():
                progress_popup.dismiss()
            Clock.schedule_once(lambda dt: show_popup("업로드 오류", f"업로드 실패: {str(e)}"))

    # 백그라운드에서 업로드 실행
    thread = threading.Thread(target=upload_thread)
    thread.daemon = True
    thread.start()

def download_from_drive():
    """다운로드 기능"""
    def download_thread():
        try:
            # 진행 상황 팝업 표시
            progress_popup = show_popup("다운로드 중", "Google Drive에 연결 중...", auto_dismiss=False)

            # Google Drive 초기화
            drive, error = init_google_drive()
            if error:
                progress_popup.dismiss()
                show_popup("다운로드 오류", error)
                return

            # 파일 검색
            Clock.schedule_once(lambda dt: setattr(progress_popup.content, 'text', "세이브 파일 검색 중..."))
            file_list = drive.ListFile({'q': f"title='{SAVEFILE_NAME}' and trashed=false"}).GetList()

            if not file_list:
                progress_popup.dismiss()
                Clock.schedule_once(lambda dt: show_popup("다운로드 오류", "Google Drive에서 세이브 파일을 찾을 수 없습니다."))
                return

            # 가장 최근 파일 다운로드
            Clock.schedule_once(lambda dt: setattr(progress_popup.content, 'text', "파일 다운로드 중..."))
            latest_file = file_list[0]  # 첫 번째 파일 (가장 최근)
            download_path = f"downloaded_{SAVEFILE_NAME}"
            latest_file.GetContentFile(download_path)

            # 세이브 폴더에 압축 해제
            Clock.schedule_once(lambda dt: setattr(progress_popup.content, 'text', "세이브 파일 복원 중..."))
            success, error = extract_save_archive(download_path)

            # 임시 파일 정리
            if os.path.exists(download_path):
                os.remove(download_path)

            progress_popup.dismiss()

            if success:
                Clock.schedule_once(lambda dt: show_popup("다운로드 성공", "세이브 파일이 성공적으로 복원되었습니다!"))
            else:
                Clock.schedule_once(lambda dt: show_popup("다운로드 오류", error))

        except Exception as e:
            if 'progress_popup' in locals():
                progress_popup.dismiss()
            Clock.schedule_once(lambda dt: show_popup("다운로드 오류", f"다운로드 실패: {str(e)}"))

    # 백그라운드에서 다운로드 실행
    thread = threading.Thread(target=download_thread)
    thread.daemon = True
    thread.start()

def show_save_folder():
    """세이브 폴더 열기"""
    try:
        if os.path.exists(STARDEW_SAVE_PATH):
            os.startfile(STARDEW_SAVE_PATH)
        else:
            show_popup("오류", "Stardew Valley 세이브 폴더를 찾을 수 없습니다.")
    except Exception as e:
        show_popup("오류", f"폴더 열기 실패: {str(e)}")

def check_save_folder():
    """세이브 폴더 상태 확인"""
    if os.path.exists(STARDEW_SAVE_PATH):
        save_folders = [f for f in os.listdir(STARDEW_SAVE_PATH) if os.path.isdir(os.path.join(STARDEW_SAVE_PATH, f))]
        if save_folders:
            return f"세이브 폴더 발견: {len(save_folders)}개의 세이브 파일"
        else:
            return "세이브 폴더가 비어있습니다"
    else:
        return "Stardew Valley 세이브 폴더를 찾을 수 없습니다"