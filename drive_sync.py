import os
import zipfile
import shutil
from pathlib import Path
from kivy.uix.popup import Popup
from kivy.uix.label import Label
from kivy.uix.boxlayout import BoxLayout
from kivy.uix.button import Button
from kivy.uix.filechooser import FileChooserListView
from kivy.clock import Clock
import threading

# Stardew Valley 기본 세이브 경로
STARDEW_SAVE_PATH = os.path.expanduser("~\\AppData\\Roaming\\StardewValley\\Saves")
TEMP_SAVE_PATH = "temp_save"
SAVEFILE_NAME = "StardewValley_Save.zip"

def show_popup(title, message, auto_dismiss=True):
    """팝업 메시지 표시"""
    popup = Popup(title=title,
                  content=Label(text=message, font_name='NanumGothic', text_size=(400, None), halign='center'),
                  size_hint=(0.7, 0.5),
                  auto_dismiss=auto_dismiss)
    popup.open()
    return popup

def create_save_archive():
    """Stardew Valley 세이브 파일들을 압축"""
    try:
        if not os.path.exists(STARDEW_SAVE_PATH):
            return None, "Stardew Valley 세이브 폴더를 찾을 수 없습니다."

        # 임시 폴더 생성
        if os.path.exists(TEMP_SAVE_PATH):
            shutil.rmtree(TEMP_SAVE_PATH)
        os.makedirs(TEMP_SAVE_PATH)

        # 세이브 파일들을 압축
        with zipfile.ZipFile(SAVEFILE_NAME, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(STARDEW_SAVE_PATH):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, STARDEW_SAVE_PATH)
                    zipf.write(file_path, arcname)

        return SAVEFILE_NAME, None
    except Exception as e:
        return None, f"압축 생성 실패: {str(e)}"

def extract_save_archive(zip_path):
    """압축 파일을 Stardew Valley 세이브 폴더에 추출"""
    try:
        # 백업 생성
        backup_path = f"{STARDEW_SAVE_PATH}_backup"
        if os.path.exists(STARDEW_SAVE_PATH):
            if os.path.exists(backup_path):
                shutil.rmtree(backup_path)
            shutil.copytree(STARDEW_SAVE_PATH, backup_path)

        # 기존 세이브 폴더 삭제
        if os.path.exists(STARDEW_SAVE_PATH):
            shutil.rmtree(STARDEW_SAVE_PATH)
        os.makedirs(STARDEW_SAVE_PATH)

        # 압축 해제
        with zipfile.ZipFile(zip_path, 'r') as zipf:
            zipf.extractall(STARDEW_SAVE_PATH)

        return True, None
    except Exception as e:
        return False, f"압축 해제 실패: {str(e)}"

def check_google_api_setup():
    """Google API 설정 파일 확인"""
    if not os.path.exists("client_secrets.json"):
        return False, "client_secrets.json 파일이 없습니다."

    try:
        import json
        with open("client_secrets.json", 'r') as f:
            config = json.load(f)
            client_id = config.get("installed", {}).get("client_id", "")
            if "YOUR_CLIENT_ID" in client_id or not client_id:
                return False, "client_secrets.json에 실제 클라이언트 ID를 설정해주세요."
        return True, None
    except Exception as e:
        return False, f"설정 파일 오류: {str(e)}"

def init_google_drive():
    """Google Drive 인증 및 초기화"""
    try:
        # API 설정 확인
        is_setup, error = check_google_api_setup()
        if not is_setup:
            return None, error

        from pydrive2.auth import GoogleAuth
        from pydrive2.drive import GoogleDrive

        gauth = GoogleAuth()

        # 저장된 인증 정보가 있으면 로드
        if os.path.exists("credentials.json"):
            gauth.LoadCredentialsFile("credentials.json")

        # 인증이 필요하면 웹 브라우저를 통해 인증
        if gauth.credentials is None:
            gauth.LocalWebserverAuth()
        elif gauth.access_token_expired:
            gauth.Refresh()
        else:
            gauth.Authorize()

        # 인증 정보 저장
        gauth.SaveCredentialsFile("credentials.json")

        drive = GoogleDrive(gauth)
        return drive, None
    except Exception as e:
        return None, f"Google Drive 인증 실패: {str(e)}"

def upload_to_drive():
    """업로드 기능"""
    def upload_thread():
        try:
            # 진행 상황 팝업 표시
            progress_popup = show_popup("업로드 중", "세이브 파일을 압축하고 있습니다...", auto_dismiss=False)

            # 세이브 파일 압축
            zip_path, error = create_save_archive()
            if error:
                progress_popup.dismiss()
                show_popup("업로드 오류", error)
                return

            # Google Drive 초기화
            Clock.schedule_once(lambda dt: setattr(progress_popup.content, 'text', "Google Drive에 연결 중..."))
            drive, error = init_google_drive()
            if error:
                progress_popup.dismiss()
                show_popup("업로드 오류", error)
                return

            # 기존 파일 검색 및 삭제
            Clock.schedule_once(lambda dt: setattr(progress_popup.content, 'text', "기존 파일 확인 중..."))
            file_list = drive.ListFile({'q': f"title='{SAVEFILE_NAME}' and trashed=false"}).GetList()
            for file in file_list:
                file.Delete()

            # 새 파일 업로드
            Clock.schedule_once(lambda dt: setattr(progress_popup.content, 'text', "파일 업로드 중..."))
            file = drive.CreateFile({'title': SAVEFILE_NAME})
            file.SetContentFile(zip_path)
            file.Upload()

            # 임시 파일 정리
            if os.path.exists(zip_path):
                os.remove(zip_path)

            progress_popup.dismiss()
            Clock.schedule_once(lambda dt: show_popup("업로드 성공", "세이브 파일이 성공적으로 업로드되었습니다!"))

        except Exception as e:
            if 'progress_popup' in locals():
                progress_popup.dismiss()
            Clock.schedule_once(lambda dt: show_popup("업로드 오류", f"업로드 실패: {str(e)}"))

    # 백그라운드에서 업로드 실행
    thread = threading.Thread(target=upload_thread)
    thread.daemon = True
    thread.start()

def download_from_drive():
    """다운로드 기능"""
    def download_thread():
        try:
            # 진행 상황 팝업 표시
            progress_popup = show_popup("다운로드 중", "Google Drive에 연결 중...", auto_dismiss=False)

            # Google Drive 초기화
            drive, error = init_google_drive()
            if error:
                progress_popup.dismiss()
                show_popup("다운로드 오류", error)
                return

            # 파일 검색
            Clock.schedule_once(lambda dt: setattr(progress_popup.content, 'text', "세이브 파일 검색 중..."))
            file_list = drive.ListFile({'q': f"title='{SAVEFILE_NAME}' and trashed=false"}).GetList()

            if not file_list:
                progress_popup.dismiss()
                Clock.schedule_once(lambda dt: show_popup("다운로드 오류", "Google Drive에서 세이브 파일을 찾을 수 없습니다."))
                return

            # 가장 최근 파일 다운로드
            Clock.schedule_once(lambda dt: setattr(progress_popup.content, 'text', "파일 다운로드 중..."))
            latest_file = file_list[0]  # 첫 번째 파일 (가장 최근)
            download_path = f"downloaded_{SAVEFILE_NAME}"
            latest_file.GetContentFile(download_path)

            # 세이브 폴더에 압축 해제
            Clock.schedule_once(lambda dt: setattr(progress_popup.content, 'text', "세이브 파일 복원 중..."))
            success, error = extract_save_archive(download_path)

            # 임시 파일 정리
            if os.path.exists(download_path):
                os.remove(download_path)

            progress_popup.dismiss()

            if success:
                Clock.schedule_once(lambda dt: show_popup("다운로드 성공", "세이브 파일이 성공적으로 복원되었습니다!"))
            else:
                Clock.schedule_once(lambda dt: show_popup("다운로드 오류", error))

        except Exception as e:
            if 'progress_popup' in locals():
                progress_popup.dismiss()
            Clock.schedule_once(lambda dt: show_popup("다운로드 오류", f"다운로드 실패: {str(e)}"))

    # 백그라운드에서 다운로드 실행
    thread = threading.Thread(target=download_thread)
    thread.daemon = True
    thread.start()

def show_save_folder():
    """세이브 폴더 열기"""
    try:
        if os.path.exists(STARDEW_SAVE_PATH):
            os.startfile(STARDEW_SAVE_PATH)
        else:
            show_popup("오류", "Stardew Valley 세이브 폴더를 찾을 수 없습니다.")
    except Exception as e:
        show_popup("오류", f"폴더 열기 실패: {str(e)}")

def check_save_folder():
    """세이브 폴더 상태 확인"""
    if os.path.exists(STARDEW_SAVE_PATH):
        save_folders = [f for f in os.listdir(STARDEW_SAVE_PATH) if os.path.isdir(os.path.join(STARDEW_SAVE_PATH, f))]
        if save_folders:
            return f"세이브 폴더 발견: {len(save_folders)}개의 세이브 파일"
        else:
            return "세이브 폴더가 비어있습니다"
    else:
        return "Stardew Valley 세이브 폴더를 찾을 수 없습니다"

# 로컬 동기화 기능 (Google API 대안)
LOCAL_SYNC_PATH = os.path.expanduser("~/Documents/StardewValley_Backup")

def create_local_backup():
    """로컬 백업 폴더에 세이브 파일 백업"""
    try:
        # 백업 폴더 생성
        if not os.path.exists(LOCAL_SYNC_PATH):
            os.makedirs(LOCAL_SYNC_PATH)

        # 현재 시간으로 백업 파일명 생성
        from datetime import datetime
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_filename = f"StardewValley_Save_{timestamp}.zip"
        backup_path = os.path.join(LOCAL_SYNC_PATH, backup_filename)

        # 세이브 파일 압축
        zip_path, error = create_save_archive()
        if error:
            return None, error

        # 백업 폴더로 복사
        shutil.copy2(zip_path, backup_path)

        # 임시 파일 삭제
        if os.path.exists(zip_path):
            os.remove(zip_path)

        return backup_path, None
    except Exception as e:
        return None, f"로컬 백업 실패: {str(e)}"

def restore_from_local_backup():
    """로컬 백업에서 가장 최근 세이브 복원"""
    try:
        if not os.path.exists(LOCAL_SYNC_PATH):
            return False, "백업 폴더가 존재하지 않습니다."

        # 백업 파일 목록 가져오기
        backup_files = [f for f in os.listdir(LOCAL_SYNC_PATH) if f.endswith('.zip')]
        if not backup_files:
            return False, "백업 파일이 없습니다."

        # 가장 최근 백업 파일 선택
        backup_files.sort(reverse=True)
        latest_backup = os.path.join(LOCAL_SYNC_PATH, backup_files[0])

        # 세이브 복원
        success, error = extract_save_archive(latest_backup)
        if success:
            return True, f"백업 파일 {backup_files[0]}에서 복원 완료"
        else:
            return False, error
    except Exception as e:
        return False, f"로컬 복원 실패: {str(e)}"

def list_local_backups():
    """로컬 백업 파일 목록 반환"""
    try:
        if not os.path.exists(LOCAL_SYNC_PATH):
            return []

        backup_files = [f for f in os.listdir(LOCAL_SYNC_PATH) if f.endswith('.zip')]
        backup_files.sort(reverse=True)
        return backup_files
    except Exception as e:
        return []

def open_backup_folder():
    """백업 폴더 열기"""
    try:
        if not os.path.exists(LOCAL_SYNC_PATH):
            os.makedirs(LOCAL_SYNC_PATH)
        os.startfile(LOCAL_SYNC_PATH)
    except Exception as e:
        show_popup("오류", f"백업 폴더 열기 실패: {str(e)}")

# 간편한 로컬 동기화 함수들
def local_backup():
    """로컬 백업 (Google Drive 대안)"""
    def backup_thread():
        try:
            progress_popup = show_popup("백업 중", "세이브 파일을 백업하고 있습니다...", auto_dismiss=False)

            backup_path, error = create_local_backup()
            progress_popup.dismiss()

            if error:
                Clock.schedule_once(lambda dt: show_popup("백업 오류", error))
            else:
                filename = os.path.basename(backup_path)
                Clock.schedule_once(lambda dt: show_popup("백업 성공", f"백업 완료: {filename}"))
        except Exception as e:
            if 'progress_popup' in locals():
                progress_popup.dismiss()
            Clock.schedule_once(lambda dt: show_popup("백업 오류", f"백업 실패: {str(e)}"))

    thread = threading.Thread(target=backup_thread)
    thread.daemon = True
    thread.start()

def local_restore():
    """로컬 복원 (Google Drive 대안)"""
    def restore_thread():
        try:
            progress_popup = show_popup("복원 중", "백업에서 세이브 파일을 복원하고 있습니다...", auto_dismiss=False)

            success, message = restore_from_local_backup()
            progress_popup.dismiss()

            if success:
                Clock.schedule_once(lambda dt: show_popup("복원 성공", message))
            else:
                Clock.schedule_once(lambda dt: show_popup("복원 오류", message))
        except Exception as e:
            if 'progress_popup' in locals():
                progress_popup.dismiss()
            Clock.schedule_once(lambda dt: show_popup("복원 오류", f"복원 실패: {str(e)}"))

    thread = threading.Thread(target=restore_thread)
    thread.daemon = True
    thread.start()