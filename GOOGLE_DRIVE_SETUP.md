# Google Drive API 설정 가이드

## 1. Google Cloud Console에서 프로젝트 생성

1. [Google Cloud Console](https://console.cloud.google.com/)에 접속
2. 새 프로젝트 생성 또는 기존 프로젝트 선택
3. "API 및 서비스" > "라이브러리"로 이동
4. "Google Drive API" 검색 후 활성화

## 2. OAuth 2.0 클라이언트 ID 생성

1. "API 및 서비스" > "사용자 인증 정보"로 이동
2. "+ 사용자 인증 정보 만들기" > "OAuth 클라이언트 ID" 선택
3. 애플리케이션 유형: "데스크톱 애플리케이션" 선택
4. 이름 입력 후 "만들기" 클릭
5. 생성된 클라이언트 ID와 클라이언트 보안 비밀번호 복사

## 3. 설정 파일 수정

### client_secrets.json 파일 수정:
```json
{
  "installed": ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
}
```

### settings.yaml 파일 수정:
```yaml
client_config_backend: settings
client_config:
  client_id: ***********************************.apps.googleusercontent.com
  client_secret: 여기에_클라이언트_보안_비밀번호_입력

save_credentials: True
save_credentials_backend: file
save_credentials_file: credentials.json

get_refresh_token: True

oauth_scope:
  - https://www.googleapis.com/auth/drive.file
  - https://www.googleapis.com/auth/drive.install
```

## 4. 첫 실행

1. 앱을 실행하고 업로드 또는 다운로드 버튼 클릭
2. 웹 브라우저가 열리면 Google 계정으로 로그인
3. 앱에 Google Drive 접근 권한 허용
4. 인증 완료 후 자동으로 credentials.json 파일이 생성됨

## 주의사항

- client_secrets.json과 credentials.json 파일은 보안상 중요하므로 공유하지 마세요
- 처음 실행 시에만 웹 브라우저 인증이 필요합니다
- 이후에는 저장된 인증 정보를 사용하여 자동으로 연결됩니다
